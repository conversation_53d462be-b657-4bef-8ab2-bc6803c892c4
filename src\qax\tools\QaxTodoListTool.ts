import { ClineMessage } from "../../shared/ExtensionMessage"

// 工具名称保持与现有系统一致，避免破坏前端和 ToolExecutor 的集成
// 在 Cline v3.24.0+ 中，工具不再需要 ToolDefinition，直接在 ToolExecutor 中处理
export const qaxTodoListToolName = "qax_todo_list"

// 在 Cline v3.24.0+ 中，工具描述和参数定义在 src/core/prompts/system.ts 中定义
// ToolExecutor.ts 中处理工具执行逻辑
// 这里只保留工具的核心逻辑函数

// Import shared types
import { QaxTodoStatus as TodoStatus, QaxTodoItem as TodoItem } from "../../shared/qax"
export type { TodoStatus, TodoItem }

/**
 * Generate a consistent ID for todo items based on content
 * This ensures the same content always gets the same ID
 */
function generateTodoId(content: string): string {
	// Simple hash function for consistent ID generation
	let hash = 0
	for (let i = 0; i < content.length; i++) {
		const char = content.charCodeAt(i)
		hash = (hash << 5) - hash + char
		hash = hash & hash // Convert to 32bit integer
	}
	return Math.abs(hash).toString(16)
}

/**
 * Parse markdown checklist string to TodoItem[].
 */
export function parseMarkdownChecklist(md: string): TodoItem[] {
	if (typeof md !== "string") {
		return []
	}
	const lines = md
		.split(/\r?\n/)
		.map((l) => l.trim())
		.filter(Boolean)
	const todos: TodoItem[] = []
	for (const line of lines) {
		const match = line.match(/^\[\s*([ xX\-~])\s*\]\s+(.+)$/)
		if (!match) {
			continue
		}
		let status: TodoStatus = "pending"
		if (match[1] === "x" || match[1] === "X") {
			status = "completed"
		} else if (match[1] === "-" || match[1] === "~") {
			status = "in_progress"
		}
		const content = match[2].trim()
		if (content) {
			todos.push({
				id: generateTodoId(content), // 使用基于内容的一致性 ID
				content,
				status,
			})
		}
	}
	return todos
}

/**
 * Get the todo list for a specific task from its state.
 */
export function getTodoListForTask(taskState: any): TodoItem[] | undefined {
	return taskState?.qaxTodoList
}

/**
 * Set the todo list for a specific task in its state.
 */
export async function setTodoListForTask(taskState: any, todos: TodoItem[]): Promise<void> {
	if (taskState) {
		taskState.qaxTodoList = todos
	}
}

/**
 * Add a new todo item to a task's todo list.
 */
export function addTodoToTask(taskState: any, content: string, status: TodoStatus = "pending"): void {
	if (!taskState) {
		return
	}

	const currentTodos = getTodoListForTask(taskState) || []
	const newTodo: TodoItem = {
		id: generateTodoId(content.trim()), // 使用一致的 ID 生成
		content: content.trim(),
		status,
	}

	taskState.qaxTodoList = [...currentTodos, newTodo]
}

/**
 * Update the status of a specific todo item in a task's todo list.
 */
export function updateTodoStatusForTask(taskState: any, todoId: string, newStatus: TodoStatus): boolean {
	if (!taskState) {
		return false
	}

	const currentTodos = getTodoListForTask(taskState)
	if (!currentTodos) {
		return false
	}

	const todoIndex = currentTodos.findIndex((todo) => todo.id === todoId)
	if (todoIndex === -1) {
		return false
	}

	const updatedTodos = [...currentTodos]
	updatedTodos[todoIndex] = { ...updatedTodos[todoIndex], status: newStatus }

	taskState.qaxTodoList = updatedTodos
	return true
}

/**
 * Delete a specific todo item from a task's todo list.
 */
export function deleteTodoFromTask(taskState: any, todoId: string): boolean {
	if (!taskState) {
		return false
	}

	const currentTodos = getTodoListForTask(taskState)
	if (!currentTodos) {
		return false
	}

	const filteredTodos = currentTodos.filter((todo) => todo.id !== todoId)
	if (filteredTodos.length === currentTodos.length) {
		return false // Todo not found
	}

	taskState.qaxTodoList = filteredTodos
	return true
}

/**
 * Extract the latest Qax todo list from ClineMessage history
 * @param clineMessages Array of ClineMessage objects
 * @returns Array of TodoItem objects or empty array if none found
 */
export function getLatestQaxTodo(clineMessages: ClineMessage[]): TodoItem[] {
	// 只处理完整的消息，排除 partial 消息以避免处理不完整的数据
	// 现在后端会在工具执行完成后发送一个 partial=false 的完整消息
	const relevantMessages = clineMessages.filter(
		(msg) =>
			((msg.type === "ask" && msg.ask === "tool") ||
				(msg.type === "say" && msg.say === "tool") ||
				(msg.type === "say" && msg.say === "qax_todo_update")) &&
			!msg.partial, // 只处理完整消息
	)

	const parsedMessages = relevantMessages.map((msg) => {
		try {
			const parsed = JSON.parse(msg.text ?? "{}")
			return parsed
		} catch (error) {
			console.log("[qax-todolist] Failed to parse message:", msg.text?.substring(0, 100))
			return null
		}
	})

	const todoMessages = parsedMessages.filter((item) => {
		if (!item || item.tool !== "qax_todo_list") {
			return false
		}
		// Support both array format (new) and string format (legacy)
		const isValid = Array.isArray(item.todos) || typeof item.todos === "string"
		return isValid
	})

	const todos = todoMessages
		.map((item) => {
			// Convert string format to array format for consistency
			if (typeof item.todos === "string") {
				return parseMarkdownChecklist(item.todos)
			}
			return item.todos
		})
		.pop()

	if (todos && Array.isArray(todos) && todos.length > 0) {
		return todos
	} else {
		return []
	}
}

/**
 * Main tool execution function for QaxTodoList
 * Note: This function is only called when block.partial is false (complete content)
 */
export async function qaxTodoListTool(taskState: any, block: any, handleError: any, pushToolResult: any) {
	// 移除了未使用的 userEdited 参数

	try {
		const todosMarkdown = block.params.todos
		if (!todosMarkdown || typeof todosMarkdown !== "string") {
			throw new Error("Invalid todos parameter: must be a non-empty string")
		}

		// Parse the markdown checklist
		const normalizedTodos = parseMarkdownChecklist(todosMarkdown)

		// Update the task state
		await setTodoListForTask(taskState, normalizedTodos)

		// Generate result message
		const todoCount = normalizedTodos.length
		const completedCount = normalizedTodos.filter((t) => t.status === "completed").length
		const inProgressCount = normalizedTodos.filter((t) => t.status === "in_progress").length
		const pendingCount = normalizedTodos.filter((t) => t.status === "pending").length

		let resultMessage = `Updated todo list with ${todoCount} item${todoCount !== 1 ? "s" : ""}.`

		if (todoCount > 0) {
			const statusParts: string[] = []
			if (pendingCount > 0) {
				statusParts.push(`${pendingCount} pending`)
			}
			if (inProgressCount > 0) {
				statusParts.push(`${inProgressCount} in progress`)
			}
			if (completedCount > 0) {
				statusParts.push(`${completedCount} completed`)
			}

			if (statusParts.length > 0) {
				resultMessage += ` Status: ${statusParts.join(", ")}.`
			}
		}

		// Send todo data as JSON message for UI consumption
		const todoMessage = JSON.stringify({
			tool: qaxTodoListToolName, // 使用统一的工具名称
			todos: normalizedTodos,
		})

		// Send both the result message and todo data
		pushToolResult([
			{
				type: "text",
				text: resultMessage,
			},
			{
				type: "text",
				text: todoMessage,
			},
		])
	} catch (error) {
		await handleError("updating todo list", error, block)
	}
}
