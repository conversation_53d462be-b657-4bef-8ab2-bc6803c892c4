import { Controller } from "../index"
import { Empty } from "@shared/proto/cline/common"
import { SyncAutocompleteStateRequest } from "@shared/proto/cline/autocomplete"

/**
 * Sync autocomplete state with send button state
 * @param controller The controller instance
 * @param request The sync request containing sending disabled state and timestamp
 * @returns Empty response
 */
export async function syncAutocompleteState(_controller: Controller, request: SyncAutocompleteStateRequest): Promise<Empty> {
	try {
		const { AutocompleteTaskManager } = await import("../../../services/autocomplete/AutocompleteTaskManager")
		const autocompleteTaskManager = AutocompleteTaskManager.getInstance()

		// Update autocomplete state based on send button state
		autocompleteTaskManager.updateStateFromSendButton(request.sendingDisabled, `webview_sync_${request.timestamp}`)
	} catch (error) {
		console.error("Failed to sync autocomplete state:", error)
	}
	return Empty.create()
}
